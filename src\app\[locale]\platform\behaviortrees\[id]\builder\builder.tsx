'use client';

/*
PROBLEMAS:
- quando edito as properties, ele sobreescreve as alteracoes das conditions e vice-versa
--- solucao 1: quando salvar so salva e fecha tudo mesmo, forcando o user a clicar no no para atualizar
--- possivel solucao 2: adicionar um retorno para o ViewNodeDrawer e atualizar la?

- quando adiciono novos fields, eles nao aparecem nos nodes (porque os seus eventos ja foram criados)
--- possivel solucao 1: colocar fields como params de dados de todos os nos
--- possivel solucao 2: atualizar manualmente os fields de cada no em um update
*/

import { BehaviorTreeController } from "@/models/behaviortree";
import { addEdge, Background, Controls, MiniMap, ReactFlow, ReactFlowProvider, useEdgesState, useNodesState, useReactFlow } from "@xyflow/react";
import { <PERSON><PERSON>, Divider, Drawer, Flex, Skeleton, Space, Typography } from "antd";
import React, { useEffect } from "react";
import TextUpdaterNode from "./buildernodes/text-uptader-node";
import '@xyflow/react/dist/style.css';
import './builder.css';
import ComponentsMenu from "./components-menu";
import { DnDProvider, NodeDataTypes, useDnD } from "./DnDContext";
import SelectorNode from "./buildernodes/selector-node";
import SequenceNode from "./buildernodes/sequence-node";
import SetPromptNode from "./buildernodes/set-prompt-node";
import SetPromptNode2 from "./buildernodes/set-prompt-node2";
import SetAgentNode, { SetAgentNodeName } from '@/components/behaviortree/node/setagent/set-agent-node'
import { Field, OptionGroup, RuleGroupType } from "react-querybuilder";
import { BaseNode, BaseNodeData, BaseNodeFormElement } from "@/components/behaviortree/node/base-node";
import ViewNodeDrawer, { ViewNodeDrawerProps } from "./drawers/view";
import SetAgentNodeDescription from "@/components/behaviortree/node/setagent/set-agent-description";
import SetAgentForm from "@/components/behaviortree/node/setagent/set-agent-form";
import { propagateServerField } from "next/dist/server/lib/render-server";
import Condition from "@/components/flowbuilder/conditions/condition";
import type { SetAgentNodeModelData } from "@/components/behaviortree/node/setagent/set-agent-model";
import { FieldModel } from "@/components/flowbuilder/variables/field-model";
import TestComponent from "./test-component";



interface BehaviorTreePutDrawerProps {
    id: string;
}

let id = 0;
const getId = () => `node_${id++}`;

interface drawerProperties {
    isOpen: boolean;
    component?: React.JSX.Element;
    title?: string;
}

interface NodeComponentType {
    description: React.ElementType;
    form: React.ElementType;
}

const nodeComponents: { [id: string]: NodeComponentType; } = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm
    }
};

/*
const nodeDataTypes = {
    [SetAgentNodeName]: {} as SetAgentNodeModelData,
}*/

const BehaviorTreeBuilder: React.FC<BehaviorTreePutDrawerProps> = (params: BehaviorTreePutDrawerProps) => {

    const behaviorTreeController = new BehaviorTreeController();
    const { data: behaviorTree, isLoading: isLoadingBehaviorTree } = behaviorTreeController.useGet([params.id as string]);

    const initialNodes: BaseNode<any>[] = [];

    const reactFlowWrapper = React.useRef(null);
    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState([]);
    const { screenToFlowPosition } = useReactFlow();
    const [type] = useDnD();

    const [treeFields, setTreeFields] = React.useState<OptionGroup<Field>[]>([]);
    const [fields, setFields] = React.useState<FieldModel[]>([]);

    const [test, setTest] = React.useState<string[]>([])
    const testRef = React.useRef(test);

    React.useEffect(() => {
        const baseObj = {input: { label: 'Inputs', options: []}, variable: { label: 'Variables', options: []}}
        fields.forEach(fd => baseObj[fd.type].options.push({name: fd.id, label: fd.name}))
        setTreeFields(Object.values(baseObj))
    }, [fields])

    React.useEffect(() => {
        console.log(treeFields);
    }, [treeFields]);


    const nodeTypes = React.useMemo(() => ({
        [SetAgentNodeName]: SetAgentNode
    }), []);

    const onConnect = React.useCallback(
        (connection: any) => setEdges((eds) => addEdge(connection, eds)),
        [setEdges],
    );

    const edgeOptions = {
        animated: true,
    };

    const onSave = () => {
        console.log('save');
    }

    const onDragOver = React.useCallback((event: DragEvent) => {
        event.preventDefault();
        if (event.dataTransfer) event.dataTransfer.dropEffect = 'move';
    }, []);

    const onChangeNodeProperties = React.useCallback(<T,>(nodeId: string, newData: T) => {
        setNodes(nodes => nodes.map((nd) => {
            if (nd.id === nodeId) {
                return {
                    ...nd,
                    data: {
                        ...nd.data,
                        baseData: {
                            ...nd.data.baseData,
                            nodeData: newData
                        }
                    }
                };
            }
            return nd;
        }));
    }, []);

    const onChangeNodeTitle = React.useCallback(<T,>(nodeId: string, title: string) => {
        setNodes(nodes => nodes.map((nd) => {
            if (nd.id === nodeId) {
                return {
                    ...nd,
                    data: {
                        ...nd.data,
                        baseData: {
                            ...nd.data.baseData,
                            name: title
                        }
                    }
                };
            }
            return nd;
        }));
    }, []);

    const onChangeNodeConditions = React.useCallback(<T,>(nodeId: string, newConditions: RuleGroupType) => {
        setNodes(nodes => nodes.map((nd) => {
            if (nd.id === nodeId) {
                return {
                    ...nd,
                    data: {
                        ...nd.data,
                        baseData: {
                            ...nd.data.baseData,
                            nodeConditions: newConditions
                        }
                    }
                };
            }
            return nd;
        }));
    }, []);

    useEffect(() => {
        testRef.current = test
        console.log("Updating refs")
    }, [test])

    const infiniteNodes = () => {
        setInterval( () => {
        const newT = ["a"]
        testRef.current.forEach((v) => newT.push(v))
        setTest(newT); 
        console.log("changing test, current len: ", testRef.current.length)}, 3000)
    }
    const testComponent = () => <TestComponent test={test} />

    const onEditNodeProperties = React.useCallback(<T,>(nodeId: string, nodeType: string, data: BaseNodeData<T>) => {
        const DrawerForm = nodeComponents[nodeType]["form"];
        const props: BaseNodeFormElement<T> = {
            data: data.nodeData,
            onChange: (nodeData) => onChangeNodeProperties(nodeId, nodeData),
            onCancel: (newNodeData) => onViewNode(nodeId, nodeType, {...data, nodeData: newNodeData ?? data.nodeData})
            //onCancel: () => onViewNode(nodeId, nodeType, data)
        };
        setDrawer({
            isOpen: true,
            title: 'Edit Node Properties',
            component: <DrawerForm {...props} />
        });
    }, [onChangeNodeProperties]);

    const onEditNodeConditions = React.useCallback(<T,>(nodeId: string, nodeType: string, data: BaseNodeData<T>) => {
        setDrawer({
            isOpen: true,
            title: 'Edit Node Rules',
            component: <Condition
                fields={treeFields}
                onChange={(newConditions) => onChangeNodeConditions(nodeId, newConditions)}
                query={data.nodeConditions}
                onCancel={(newConditions) => onViewNode(nodeId, nodeType, {...data, nodeConditions: newConditions ?? data.nodeConditions})}
                //onCancel={() => onViewNode(nodeId, nodeType, data)}
            />
        });
    }, [treeFields, onChangeNodeConditions]);

    const onViewNode = React.useCallback(<T,>(nodeId: string, nodeType: string, node: BaseNodeData<T>) => {
        const DrawerDescription = nodeComponents[nodeType]["description"];
        const props: ViewNodeDrawerProps<T> = {
            node: node,
            nodeDescription: <DrawerDescription {...node.nodeData} />,
            treeFields: treeFields,
            onEditProperties: (data) => onEditNodeProperties(nodeId, nodeType, data),
            onEditConditions: (data) => onEditNodeConditions(nodeId, nodeType, data),
            onChangeTitle: (title) => onChangeNodeTitle(nodeId, title)
        };
        setDrawer({
            isOpen: true,
            title: 'Viewing Node',
            component: <ViewNodeDrawer<T> {...props} nodes={testRef.current} nodes2={test} getComponent={testComponent} />
        });
    }, [treeFields, onEditNodeProperties, onEditNodeConditions, nodes]);

    const buildEvents = React.useCallback(<K extends keyof typeof NodeDataTypes>(typeKey: K, nodeId: string) => {
        return {
            onViewNode: (data: BaseNodeData<typeof NodeDataTypes[K]>) =>
                onViewNode<typeof NodeDataTypes[K]>(nodeId, typeKey, data),
        };
    }, [onViewNode]);

    const onDrop = React.useCallback((event: DragEvent) => {
        event.preventDefault();

        if (!type) {
            return;
        }

        const position = screenToFlowPosition({
            x: event.clientX,
            y: event.clientY,
        });

        const nodeId = getId();

        const newNode = {
            id: nodeId,
            type: type as string,
            position,
            data: {
                baseData: {
                    name: `${type} node`,
                    nodeData: {} as typeof NodeDataTypes[typeof type]
                },
                events: buildEvents(type, nodeId)
            }
        };

        setNodes(nodes => [...nodes, newNode]);
    }, [screenToFlowPosition, type, buildEvents]);

    const [drawer, setDrawer] = React.useState<drawerProperties>({ isOpen: false });
    const onCloseDrawer = () => {
        setDrawer({ isOpen: false });
    };




    if (isLoadingBehaviorTree || !behaviorTree) {
        return <Skeleton />
    }

    return (
        <>

            <Drawer
                title={drawer.title}
                open={drawer.isOpen}
                onClose={onCloseDrawer}
                size="large"
            >
                {drawer.component}
            </Drawer>
            <Button onClick={infiniteNodes}>asdasdasd</Button>
            <Flex style={{ height: '100%' }} className="dndflow" justify='stretch'>
                <div className="reactflow-wrapper" ref={reactFlowWrapper}>
                    <ReactFlow
                        nodes={nodes}
                        edges={edges}
                        fitView
                        nodeTypes={nodeTypes}
                        defaultEdgeOptions={edgeOptions}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                        onConnect={onConnect}
                        onDrop={onDrop}
                        onDragOver={onDragOver}
                    >
                        <Controls />
                        <MiniMap />
                        <Background variant="dots" gap={12} size={1} />
                    </ReactFlow>
                </div>
                <ComponentsMenu onChangeField={(values: FieldModel) => {
                    setFields((fds) => {
                        const ret = [...fds.filter(fd => fd.id != values.id)]
                        ret.push(values)
                        return ret
                    })
                }}
                    fields={fields}
                />
            </Flex>
        </>
    )
}



export default ({ id }: { id: string }) => {

    const onSave = () => {
        console.log('save');
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Typography.Title level={2}>
                    Behavior Builder for 'asd'
                </Typography.Title>
                <Button size='large' type='primary' onClick={() => onSave()}>
                    Save
                </Button>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider>
                        <BehaviorTreeBuilder id={id} />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
}
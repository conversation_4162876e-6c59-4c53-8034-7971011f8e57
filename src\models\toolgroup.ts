import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";
import { AgentTool } from "./agenttool";

export interface ToolGroup extends IBasemodel {
    id: string;
    name: string;
    description: string;
    toolIds: string[];
    tools?: AgentTool[]; // Populated when fetching with tools
}

export enum ToolGroupActions {
    EDIT,
    DELETE,
    DUPLICATE,
    VIEW_TOOLS
}

export interface ToolGroupCreateRequest {
    name: string;
    description: string;
    toolIds: string[];
}

export interface ToolGroupUpdateRequest {
    id: string;
    name: string;
    description: string;
    toolIds: string[];
}

export interface ToolGroupWithToolsResponse extends ToolGroup {
    tools: AgentTool[];
}

export class ToolGroupController implements IBasemodelController<ToolGroup, ToolGroupActions> {
    getId: (item: ToolGroup) => string = (item) => item.id;

    useGet = (ids: string[], options?: SWRConfiguration) =>
        Fetcher2.SWRMultiTemplate<ToolGroup>(
            ids.map((id) => APIRoutes.ToolGroupController.GET.replace("{id}", id)),
            { method: 'GET' },
            options
        )

    useGetWithTools = (ids: string[], options?: SWRConfiguration) =>
        Fetcher2.SWRMultiTemplate<ToolGroupWithToolsResponse>(
            ids.map((id) => APIRoutes.ToolGroupController.GET_WITH_TOOLS.replace("{id}", id)),
            { method: 'GET' },
            options
        )

    can = (action: ToolGroupActions, onItem: ToolGroup) => {
        switch (action) {
            case ToolGroupActions.EDIT:
            case ToolGroupActions.DELETE:
            case ToolGroupActions.DUPLICATE:
            case ToolGroupActions.VIEW_TOOLS:
                return true;
            default:
                return false;
        }
    }

    useList = (request: ListRequest) =>
        Fetcher2.SWRTemplate<ListResponse<ToolGroup>>(
            APIRoutes.ToolGroupController.LIST,
            { method: 'GET', queryString: request }
        )

    useListWithTools = (request: ListRequest) =>
        Fetcher2.SWRTemplate<ListResponse<ToolGroupWithToolsResponse>>(
            APIRoutes.ToolGroupController.LIST_WITH_TOOLS,
            { method: 'GET', queryString: request }
        )

    useDelete = (id: string) =>
        Fetcher2.SWRMutationTemplate<ToolGroup>(
            APIRoutes.ToolGroupController.DELETE,
            { method: 'DELETE', urlPlaceholders: { id: id } }
        )

    useCreate = () =>
        Fetcher2.SWRMutationTemplate<ToolGroup>(
            APIRoutes.ToolGroupController.CREATE,
            { method: 'POST' }
        )

    useUpdate = () =>
        Fetcher2.SWRMutationTemplate<ToolGroup>(
            APIRoutes.ToolGroupController.EDIT,
            { method: 'PUT' }
        )

    useDuplicate = (id: string) =>
        Fetcher2.SWRMutationTemplate<ToolGroup>(
            APIRoutes.ToolGroupController.DUPLICATE,
            { method: 'POST', urlPlaceholders: { id: id } }
        )
}

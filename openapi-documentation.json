{"openapi": "3.0.3", "info": {"title": "Coral Agents API", "description": "API documentation for the Coral Agents platform, providing endpoints for managing agents, knowledge bases, flows, traces, and related resources.", "version": "1.0.0", "contact": {"name": "Coral Agents Support"}}, "servers": [{"url": "http://localhost:5083", "description": "Local development server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"IBasemodel": {"type": "object", "properties": {"lastChangeTimestamp": {"type": "number", "description": "Timestamp of last modification"}, "createdAt": {"type": "number", "description": "Creation timestamp"}}, "required": ["lastChangeTimestamp", "createdAt"]}, "ListRequest": {"type": "object", "properties": {"count": {"type": "integer", "description": "Number of items to return"}, "nextToken": {"type": "string", "description": "Token for pagination"}}, "required": ["count"]}, "ListResponse": {"type": "object", "properties": {"entries": {"type": "array", "items": {"type": "object"}, "description": "Array of returned items"}, "nextToken": {"type": "string", "description": "Token for next page"}, "total": {"type": "integer", "description": "Total number of items"}}, "required": ["entries", "total"]}, "DeleteMultiRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}, "description": "Array of IDs to delete"}}, "required": ["ids"]}, "DeleteMultiResponse": {"type": "object", "properties": {"deletedIds": {"type": "array", "items": {"type": "string"}, "description": "Array of successfully deleted IDs"}}, "required": ["deletedIds"]}, "SigninRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User email address"}, "password": {"type": "string", "description": "User password"}}, "required": ["email", "password"]}, "SigninResponse": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "Session identifier"}, "status": {"type": "string", "enum": ["CREDENTIALS_NOT_FOUND", "EMAIL_NOT_CONFIRMED", "NEW_PASSWORD_REQUIRED", "MFA_REQUIRED", "LOGIN_SUCCESSFUL", "INTERNAL_ERROR"], "description": "Authentication status"}, "userId": {"type": "string", "description": "User identifier"}, "accountId": {"type": "string", "description": "Account identifier"}, "accessToken": {"type": "string", "description": "Access token"}, "refreshToken": {"type": "string", "description": "Refresh token"}, "jwt": {"type": "string", "description": "JWT token"}, "RefreshTimeout": {"type": "number", "description": "Token refresh timeout"}}, "required": ["sessionId", "RefreshTimeout"]}, "UpdateJwtResponse": {"type": "object", "properties": {"jwt": {"type": "string", "description": "Updated JWT token"}}}, "Agent": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"agentId": {"type": "string", "description": "Unique agent identifier"}, "name": {"type": "string", "description": "Agent name"}, "description": {"type": "string", "description": "Agent description"}, "instructions": {"type": "string", "description": "Agent instructions"}, "agentType": {"type": "string", "enum": ["Unknown", "Claude_v2"], "description": "Type of agent"}, "status": {"type": "string", "enum": ["QUEUED", "DB_ENTRY_CREATED", "CREATING", "UPDATING", "READY", "SEALING", "PREPARING", "VERSIONING", "WAITING_VERSION", "TAGGING", "Unknown"], "description": "Current agent status"}}, "required": ["agentId", "name", "description", "instructions", "agentType", "status"]}]}, "AgentSearchRequest": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "limit": {"type": "integer", "description": "Maximum number of results"}}, "required": ["query"]}, "KnowledgeBase": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"kbId": {"type": "string", "description": "Unique knowledge base identifier"}, "name": {"type": "string", "description": "Knowledge base name"}, "description": {"type": "string", "description": "Knowledge base description"}, "status": {"type": "string", "enum": ["QUEUED", "DB_ENTRY_CREATED", "KB_CREATED", "DATASOURCE_CREATED", "READY", "SEALING", "DEPLOYING", "SYNCRONIZING", "TAGGING", "DISABLED"], "description": "Current knowledge base status"}}, "required": ["kbId", "name", "description", "status"]}]}, "KnowledgeBaseCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Knowledge base name"}, "description": {"type": "string", "description": "Knowledge base description"}}, "required": ["name", "description"]}, "KnowledgebaseFile": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"fileId": {"type": "string", "description": "Unique file identifier"}, "fileName": {"type": "string", "description": "Original file name"}, "uploadedFileSize": {"type": "number", "description": "Size of uploaded file in bytes"}, "isDeployed": {"type": "boolean", "description": "Whether file is deployed"}, "status": {"type": "string", "enum": ["QUEUED", "UPLOADING", "DELETED", "READY"], "description": "File upload status"}}, "required": ["fileId", "fileName", "uploadedFileSize", "isDeployed", "status"]}]}, "AgentKnowledgebase": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"agentId": {"type": "string", "description": "Agent identifier"}, "kbId": {"type": "string", "description": "Knowledge base identifier"}}, "required": ["agentId", "kbId"]}]}, "AssignAgentKnowledgebaseRequest": {"type": "object", "properties": {"agentId": {"type": "string", "description": "Agent identifier"}, "kbId": {"type": "string", "description": "Knowledge base identifier"}, "description": {"type": "string", "description": "Assignment description"}}, "required": ["agentId", "kbId", "description"]}, "ApiKey": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"name": {"type": "string", "description": "API key name"}, "key": {"type": "string", "description": "API key value"}, "secret": {"type": "string", "description": "API key secret"}, "claim": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "description": "API key claims"}, "paused": {"type": "boolean", "description": "Whether API key is paused"}, "expireAt": {"type": "number", "description": "Expiration timestamp"}, "OwnerUserId": {"type": "string", "description": "Owner user identifier"}}, "required": ["name", "key", "secret", "claim", "paused", "expireAt", "OwnerUserId"]}]}, "Claim": {"type": "object", "properties": {"type": {"type": "string", "description": "Claim type"}, "value": {"type": "string", "description": "Claim value"}}, "required": ["type", "value"]}, "ApiKeyCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "API key name"}, "expireAt": {"type": "number", "description": "Expiration timestamp"}, "claims": {"type": "array", "items": {"$ref": "#/components/schemas/Claim"}, "description": "API key claims"}, "personalKey": {"type": "boolean", "description": "Whether this is a personal key"}}, "required": ["name", "expireAt", "claims", "<PERSON><PERSON><PERSON>"]}, "AgentAlias": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"agentId": {"type": "string", "description": "Agent identifier"}, "alias": {"type": "string", "description": "Agent alias name"}, "description": {"type": "string", "description": "<PERSON>as description"}, "agentTag": {"type": "string", "description": "Associated agent tag"}, "agentName": {"type": "string", "description": "Agent name"}}, "required": ["agentId", "alias", "description", "agentTag", "<PERSON><PERSON><PERSON>"]}]}, "AgentAliasCreateRequest": {"type": "object", "properties": {"alias": {"type": "string", "description": "Agent alias name"}, "description": {"type": "string", "description": "<PERSON>as description"}, "agentTag": {"type": "string", "description": "Associated agent tag"}}, "required": ["alias", "description", "agentTag"]}, "BehaviorTree": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Behavior tree identifier"}, "name": {"type": "string", "description": "Behavior tree name"}, "description": {"type": "string", "description": "Behavior tree description"}, "treeDefinition": {"type": "string", "description": "Tree definition JSON"}}, "required": ["id", "name", "description"]}]}, "BehaviorTreeCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Behavior tree name"}, "description": {"type": "string", "description": "Behavior tree description"}}, "required": ["name", "description"]}, "Flow": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Flow identifier"}, "name": {"type": "string", "description": "Flow name"}, "description": {"type": "string", "description": "Flow description"}, "currentTagId": {"type": "string", "description": "Current tag identifier"}, "flowDefinition": {"type": "string", "description": "Flow definition JSON"}}, "required": ["id", "name", "description"]}]}, "FlowCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Flow name"}, "description": {"type": "string", "description": "Flow description"}}, "required": ["name", "description"]}, "FlowUpdateRequest": {"type": "object", "properties": {"id": {"type": "string", "description": "Flow identifier"}, "name": {"type": "string", "description": "Flow name"}, "description": {"type": "string", "description": "Flow description"}, "flowDefinition": {"type": "string", "description": "Flow definition JSON"}}, "required": ["id", "name", "description"]}, "FlowTag": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Flow tag identifier"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "tagName": {"type": "string", "description": "Tag name"}, "flowDefinition": {"type": "string", "description": "Flow definition JSON"}, "version": {"type": "number", "description": "Tag version number"}, "isLatest": {"type": "boolean", "description": "Whether this is the latest tag"}, "flowName": {"type": "string", "description": "Flow name"}}, "required": ["id", "flowId", "tagName", "flowDefinition", "version", "isLatest"]}]}, "FlowTagCreateRequest": {"type": "object", "properties": {"flowId": {"type": "string", "description": "Flow identifier"}, "tagName": {"type": "string", "description": "Tag name"}, "flowDefinition": {"type": "string", "description": "Flow definition JSON"}, "setAsLatest": {"type": "boolean", "description": "Whether to set as latest tag"}}, "required": ["flowId", "tagName", "flowDefinition"]}, "FlowAlias": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Flow alias identifier"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "alias": {"type": "string", "description": "<PERSON><PERSON> name"}, "description": {"type": "string", "description": "<PERSON>as description"}, "flowTagId": {"type": "string", "description": "Associated flow tag identifier"}, "flowName": {"type": "string", "description": "Flow name"}, "tagName": {"type": "string", "description": "Tag name"}}, "required": ["id", "flowId", "alias", "description", "flowTagId"]}]}, "FlowAliasCreateRequest": {"type": "object", "properties": {"flowId": {"type": "string", "description": "Flow identifier"}, "alias": {"type": "string", "description": "<PERSON><PERSON> name"}, "description": {"type": "string", "description": "<PERSON>as description"}, "flowTagId": {"type": "string", "description": "Associated flow tag identifier"}}, "required": ["flowId", "alias", "description", "flowTagId"]}, "Trace": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "Trace identifier"}, "accountId": {"type": "string", "description": "Account identifier"}, "traceTime": {"type": "number", "description": "Trace timestamp"}, "sessionId": {"type": "string", "description": "Session identifier"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "flowName": {"type": "string", "description": "Flow name"}, "status": {"type": "string", "enum": ["RUNNING", "COMPLETED", "FAILED", "CANCELLED"], "description": "Trace status"}}, "required": ["id", "accountId", "traceTime", "sessionId", "status"]}]}, "TraceSearchRequest": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "limit": {"type": "integer", "description": "Maximum number of results"}}, "required": ["query"]}, "MCPServer": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"id": {"type": "string", "description": "MCP Server identifier"}, "name": {"type": "string", "description": "MCP Server name"}, "description": {"type": "string", "description": "MCP Server description"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "flowAlias": {"type": "string", "description": "<PERSON> alias"}, "status": {"type": "string", "enum": ["CREATED", "DEPLOYING", "RUNNING", "STOPPED", "FAILED", "UPDATING"], "description": "MCP Server status"}, "deployedAt": {"type": "number", "description": "Deployment timestamp"}, "endpoint": {"type": "string", "description": "Server endpoint URL"}, "flowName": {"type": "string", "description": "Flow name"}}, "required": ["id", "name", "description", "flowId", "flowAlias", "status"]}]}, "MCPServerCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "MCP Server name"}, "description": {"type": "string", "description": "MCP Server description"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "flowAlias": {"type": "string", "description": "<PERSON> alias"}}, "required": ["name", "description", "flowId", "flowAlias"]}, "MCPServerUpdateRequest": {"type": "object", "properties": {"id": {"type": "string", "description": "MCP Server identifier"}, "name": {"type": "string", "description": "MCP Server name"}, "description": {"type": "string", "description": "MCP Server description"}, "flowId": {"type": "string", "description": "Associated flow identifier"}, "flowAlias": {"type": "string", "description": "<PERSON> alias"}}, "required": ["id", "name", "description", "flowId", "flowAlias"]}, "AgentTag": {"allOf": [{"$ref": "#/components/schemas/IBasemodel"}, {"type": "object", "properties": {"agentId": {"type": "number", "description": "Agent identifier"}, "tag": {"type": "string", "description": "Tag name"}}, "required": ["agentId", "tag"]}]}}}, "paths": {"/signin": {"post": {"tags": ["Authentication"], "summary": "User sign in", "description": "Authenticate user with email and password", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigninRequest"}}}}, "responses": {"200": {"description": "Authentication response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigninResponse"}}}}, "401": {"description": "Authentication failed"}, "500": {"description": "Internal server error"}}}}, "/signin/update": {"post": {"tags": ["Authentication"], "summary": "Update JWT token", "description": "Refresh the JWT token for authenticated user", "responses": {"200": {"description": "Updated JWT token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateJwtResponse"}}}}, "401": {"description": "Unauthorized"}}}}, "/agent": {"get": {"tags": ["Agents"], "summary": "List agents", "description": "Retrieve a paginated list of agents", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of agents to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of agents", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}}}]}}}}, "401": {"description": "Unauthorized"}}}}, "/agent/search": {"get": {"tags": ["Agents"], "summary": "Search agents", "description": "Search for agents using a query string", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Search query"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 100}, "description": "Maximum number of results"}], "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}}}]}}}}, "401": {"description": "Unauthorized"}}}}, "/agent/{agentId}": {"get": {"tags": ["Agents"], "summary": "Get agent by ID", "description": "Retrieve a specific agent by its ID", "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Agent identifier"}], "responses": {"200": {"description": "Agent details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "404": {"description": "Agent not found"}, "401": {"description": "Unauthorized"}}}, "put": {"tags": ["Agents"], "summary": "Update agent", "description": "Update an existing agent", "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Agent identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "responses": {"200": {"description": "Agent updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "404": {"description": "Agent not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Agents"], "summary": "Delete agent", "description": "Delete a specific agent", "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Agent identifier"}], "responses": {"200": {"description": "Agent deleted successfully"}, "404": {"description": "Agent not found"}, "401": {"description": "Unauthorized"}}}}, "/agent/{agentId}/deploy": {"post": {"tags": ["Agents"], "summary": "Deploy agent", "description": "Deploy an agent for use", "parameters": [{"name": "agentId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Agent identifier"}], "responses": {"200": {"description": "Agent deployed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Agent"}}}}, "404": {"description": "Agent not found"}, "401": {"description": "Unauthorized"}}}}, "/knowledgebase": {"get": {"tags": ["Knowledge Base"], "summary": "List knowledge bases", "description": "Retrieve a paginated list of knowledge bases", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of knowledge bases to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of knowledge bases", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/KnowledgeBase"}}}}]}}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Knowledge Base"], "summary": "Create knowledge base", "description": "Create a new knowledge base", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBaseCreateRequest"}}}}, "responses": {"201": {"description": "Knowledge base created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBase"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}}}, "/knowledgebase/{kbId}": {"get": {"tags": ["Knowledge Base"], "summary": "Get knowledge base by ID", "description": "Retrieve a specific knowledge base by its ID", "parameters": [{"name": "kbId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Knowledge base identifier"}], "responses": {"200": {"description": "Knowledge base details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBase"}}}}, "404": {"description": "Knowledge base not found"}, "401": {"description": "Unauthorized"}}}, "put": {"tags": ["Knowledge Base"], "summary": "Update knowledge base", "description": "Update an existing knowledge base", "parameters": [{"name": "kbId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Knowledge base identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBase"}}}}, "responses": {"200": {"description": "Knowledge base updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBase"}}}}, "404": {"description": "Knowledge base not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Knowledge Base"], "summary": "Delete knowledge base", "description": "Delete a specific knowledge base", "parameters": [{"name": "kbId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Knowledge base identifier"}], "responses": {"200": {"description": "Knowledge base deleted successfully"}, "404": {"description": "Knowledge base not found"}, "401": {"description": "Unauthorized"}}}}, "/knowledgebase/{kbId}/deploy": {"post": {"tags": ["Knowledge Base"], "summary": "Deploy knowledge base", "description": "Deploy a knowledge base for use", "parameters": [{"name": "kbId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Knowledge base identifier"}], "responses": {"200": {"description": "Knowledge base deployed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KnowledgeBase"}}}}, "404": {"description": "Knowledge base not found"}, "401": {"description": "Unauthorized"}}}}, "/apikey": {"get": {"tags": ["API Keys"], "summary": "List API keys", "description": "Retrieve a paginated list of API keys", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of API keys to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of API keys", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/ApiKey"}}}}]}}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["API Keys"], "summary": "Create API key", "description": "Create a new API key", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyCreateRequest"}}}}, "responses": {"201": {"description": "API key created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKey"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}}}, "/apikey/{apiKey}": {"get": {"tags": ["API Keys"], "summary": "Get API key by key", "description": "Retrieve a specific API key by its key value", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}, "description": "API key value"}], "responses": {"200": {"description": "API key details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKey"}}}}, "404": {"description": "API key not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["API Keys"], "summary": "Delete API key", "description": "Delete a specific API key", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}, "description": "API key value"}], "responses": {"200": {"description": "API key deleted successfully"}, "404": {"description": "API key not found"}, "401": {"description": "Unauthorized"}}}}, "/flow": {"get": {"tags": ["Flows"], "summary": "List flows", "description": "Retrieve a paginated list of flows", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of flows to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of flows", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/Flow"}}}}]}}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["Flows"], "summary": "Create flow", "description": "Create a new flow", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowCreateRequest"}}}}, "responses": {"201": {"description": "Flow created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}}}, "/flow/{id}": {"get": {"tags": ["Flows"], "summary": "Get flow by ID", "description": "Retrieve a specific flow by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Flow identifier"}], "responses": {"200": {"description": "Flow details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "404": {"description": "Flow not found"}, "401": {"description": "Unauthorized"}}}, "put": {"tags": ["Flows"], "summary": "Update flow", "description": "Update an existing flow", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Flow identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlowUpdateRequest"}}}}, "responses": {"200": {"description": "Flow updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Flow"}}}}, "404": {"description": "Flow not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Flows"], "summary": "Delete flow", "description": "Delete a specific flow", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Flow identifier"}], "responses": {"200": {"description": "Flow deleted successfully"}, "404": {"description": "Flow not found"}, "401": {"description": "Unauthorized"}}}}, "/trace": {"get": {"tags": ["Traces"], "summary": "List traces", "description": "Retrieve a paginated list of traces", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of traces to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of traces", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/Trace"}}}}]}}}}, "401": {"description": "Unauthorized"}}}}, "/trace/search": {"get": {"tags": ["Traces"], "summary": "Search traces", "description": "Search for traces using a query string", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Search query"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 100}, "description": "Maximum number of results"}], "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/Trace"}}}}]}}}}, "401": {"description": "Unauthorized"}}}}, "/trace/{id}": {"get": {"tags": ["Traces"], "summary": "Get trace by ID", "description": "Retrieve a specific trace by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Trace identifier"}], "responses": {"200": {"description": "Trace details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Trace"}}}}, "404": {"description": "Trace not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Traces"], "summary": "Delete trace", "description": "Delete a specific trace", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Trace identifier"}], "responses": {"200": {"description": "Trace deleted successfully"}, "404": {"description": "Trace not found"}, "401": {"description": "Unauthorized"}}}}, "/kbfiles": {"get": {"tags": ["Knowledge Base Files"], "summary": "List knowledge base files", "description": "Retrieve a list of files for knowledge bases", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of files to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of knowledge base files", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/KnowledgebaseFile"}}}}]}}}}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["Knowledge Base Files"], "summary": "Delete multiple files", "description": "Delete multiple knowledge base files", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMultiRequest"}}}}, "responses": {"200": {"description": "Files deleted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMultiResponse"}}}}, "401": {"description": "Unauthorized"}}}}, "/mcpserver": {"get": {"tags": ["MCP Servers"], "summary": "List MCP servers", "description": "Retrieve a paginated list of MCP servers", "parameters": [{"name": "count", "in": "query", "required": true, "schema": {"type": "integer"}, "description": "Number of MCP servers to return"}, {"name": "nextToken", "in": "query", "schema": {"type": "string"}, "description": "Pagination token"}], "responses": {"200": {"description": "List of MCP servers", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ListResponse"}, {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/MCPServer"}}}}]}}}}, "401": {"description": "Unauthorized"}}}, "post": {"tags": ["MCP Servers"], "summary": "Create MCP server", "description": "Create a new MCP server", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServerCreateRequest"}}}}, "responses": {"201": {"description": "MCP server created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}}}, "/mcpserver/{id}": {"get": {"tags": ["MCP Servers"], "summary": "Get MCP server by ID", "description": "Retrieve a specific MCP server by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "responses": {"200": {"description": "MCP server details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}, "put": {"tags": ["MCP Servers"], "summary": "Update MCP server", "description": "Update an existing MCP server", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServerUpdateRequest"}}}}, "responses": {"200": {"description": "MCP server updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}, "delete": {"tags": ["MCP Servers"], "summary": "Delete MCP server", "description": "Delete a specific MCP server", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "responses": {"200": {"description": "MCP server deleted successfully"}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}}, "/mcpserver/{id}/deploy": {"post": {"tags": ["MCP Servers"], "summary": "Deploy MCP server", "description": "Deploy an MCP server", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "responses": {"200": {"description": "MCP server deployed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}}, "/mcpserver/{id}/stop": {"post": {"tags": ["MCP Servers"], "summary": "Stop MCP server", "description": "Stop a running MCP server", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "responses": {"200": {"description": "MCP server stopped successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}}, "/mcpserver/{id}/restart": {"post": {"tags": ["MCP Servers"], "summary": "Restart MCP server", "description": "Restart an MCP server", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "MCP server identifier"}], "responses": {"200": {"description": "MCP server restarted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPServer"}}}}, "404": {"description": "MCP server not found"}, "401": {"description": "Unauthorized"}}}}, "/agentkb/assign": {"post": {"tags": ["Agent Knowledge Base"], "summary": "Assign knowledge base to agent", "description": "Assign a knowledge base to an agent", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignAgentKnowledgebaseRequest"}}}}, "responses": {"200": {"description": "Knowledge base assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentKnowledgebase"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "Unauthorized"}}}}, "/agentkb/exists": {"get": {"tags": ["Agent Knowledge Base"], "summary": "Check if agent-knowledge base assignment exists", "description": "Check if a specific agent-knowledge base assignment exists", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Agent identifier"}, {"name": "kbId", "in": "query", "required": true, "schema": {"type": "string"}, "description": "Knowledge base identifier"}], "responses": {"200": {"description": "Assignment exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"exists": {"type": "boolean", "description": "Whether the assignment exists"}}}}}}, "401": {"description": "Unauthorized"}}}}}}
import DrawerTemplate, { DrawerTemplateErrorEmpty, makeDrawerFormFields } from "@/templates/drawer-template";
import { Form, Input } from "antd";
import React, { useEffect } from "react";
import '@ant-design/v5-patch-for-react-19';
import { Fetcher } from "@/functions/fetcher";
import SubmitButton from "@/components/submit-button";
import { Flow, FlowController } from "@/models/flow";

interface FlowPutDrawerParams {
    isOpen: boolean;
    flow?: Flow;
    onSuccess: (data: Flow) => void;
    mode: 'edit' | 'create';
}

const FlowPutDrawer: React.FC<FlowPutDrawerParams> = (params: FlowPutDrawerParams) => {
    const flowController: FlowController = new FlowController();

    const [form] = Form.useForm<Flow>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty)
    const { data, trigger, isMutating } = (params.mode === 'create') ? flowController.useCreate() : flowController.useUpdate();
    const [fieldsData, setFieldsData] = React.useState<any[]>([]);

    useEffect(() => {
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [data])

    useEffect(() => {
        if (params.isOpen) {
            form.resetFields();
            setDrawerError(DrawerTemplateErrorEmpty);
            setFieldsData(makeDrawerFormFields(params.flow));
        }
    }, [params.isOpen])

    const onFinish = (values: Flow) => {
        let request = { name: values.name, description: values.description }
        trigger({ body: request }).catch((reason: Fetcher.FetcherError) => {
            setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
        });
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title={(params.mode === 'create') ? 'Create a Flow' : 'Editing ' + params.flow?.name}
        >
            <Form form={form} fields={fieldsData} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[{ max: 64, message: 'Please input up to 64 characters.' }, { min: 3, message: 'Your flow name must have at least 3 characters.' }, { required: true, message: 'Please input a name for the flow' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{ max: 250, message: 'Please input a description of up to 250 characters' }]}
                    required
                >
                    <Input.TextArea rows={4} count={{
                        show: true,
                        max: 250,
                    }} />
                </Form.Item>
                <Form.Item>
                    <SubmitButton form={form}>{(params.mode === 'create') ? 'Create' : 'Save'}</SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    )
}

export default FlowPutDrawer;
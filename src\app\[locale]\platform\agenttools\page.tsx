'use client'

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, <PERSON>, Typography, Tabs } from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  FunctionOutlined,
  GroupOutlined
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import ConfirmDeleteModal from '@/components/confirm-delete-modal';
import { AgentTool, AgentToolController } from '@/models/agenttool';
import { ToolGroup, ToolGroupController } from '@/models/toolgroup';
import AgentToolList from './lists/agenttool-list';
import ToolGroupList from './lists/toolgroup-list';
import AgentToolEditDrawer from './drawers/agenttool-edit-drawer';
import AgentToolViewDrawer from './drawers/agenttool-view-drawer';
import ToolGroupEditDrawer from './drawers/toolgroup-edit-drawer';
import ToolGroupViewDrawer from './drawers/toolgroup-view-drawer';

type AgentToolsComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
  agentTool?: AgentTool;
  toolGroup?: ToolGroup;
  open: boolean;
  type: 'tool' | 'group';
}

const AgentToolsComponent: React.FC<AgentToolsComponentProps> = (props: AgentToolsComponentProps) => {
  const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
  const [mutateToolObject, setMutateToolObject] = React.useState<AgentTool[]>([]);
  const [mutateGroupObject, setMutateGroupObject] = React.useState<ToolGroup[]>([]);
  const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false, type: 'tool' });
  const [activeTab, setActiveTab] = React.useState('tools');

  const onCloseDrawer = () => {
    setDrawerOptions(emptyDrawer);
  };

  // Agent Tool handlers
  const onEditTool = (record: AgentTool) => {
    setDrawerOptions({
      title: "Edit Agent Tool",
      isOpen: true,
      component: <AgentToolEditDrawer
        agentTool={record}
        isOpen={true}
        onSuccess={(data: AgentTool) => { setDrawerOptions(emptyDrawer); setMutateToolObject([data]) }}
        mode='edit'
      />
    })
  }

  const onCreateTool = () => {
    setDrawerOptions({
      title: "Create Agent Tool",
      isOpen: true,
      component: <AgentToolEditDrawer
        isOpen={true}
        onSuccess={(data: AgentTool) => { setDrawerOptions(emptyDrawer); setMutateToolObject([data]) }}
        mode='create'
      />
    })
  }

  const onViewTool = (record: AgentTool) => {
    setDrawerOptions({
      title: "View Agent Tool",
      isOpen: true,
      component: <AgentToolViewDrawer
        agentTool={record}
        onDelete={(record: AgentTool) => onDeleteTool(record)}
        onEdit={(record: AgentTool) => onEditTool(record)}
        onDeploy={(record: AgentTool) => onDeployTool(record)}
        onValidate={(record: AgentTool) => onValidateTool(record)}
        onDuplicate={(record: AgentTool) => onDuplicateTool(record)}
      />
    })
  }

  const onDeleteTool = (record: AgentTool) => {
    setDeleteModalOptions({ agentTool: record, open: true, type: 'tool' });
  }

  const onDeployTool = (record: AgentTool) => {
    // TODO: Implement deploy functionality
    console.log('Deploy tool:', record);
  }

  const onValidateTool = (record: AgentTool) => {
    // TODO: Implement validate functionality
    console.log('Validate tool:', record);
  }

  const onDuplicateTool = (record: AgentTool) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate tool:', record);
  }

  // Tool Group handlers
  const onEditGroup = (record: ToolGroup) => {
    setDrawerOptions({
      title: "Edit Tool Group",
      isOpen: true,
      component: <ToolGroupEditDrawer
        toolGroup={record}
        isOpen={true}
        onSuccess={(data: ToolGroup) => { setDrawerOptions(emptyDrawer); setMutateGroupObject([data]) }}
        mode='edit'
      />
    })
  }

  const onCreateGroup = () => {
    setDrawerOptions({
      title: "Create Tool Group",
      isOpen: true,
      component: <ToolGroupEditDrawer
        isOpen={true}
        onSuccess={(data: ToolGroup) => { setDrawerOptions(emptyDrawer); setMutateGroupObject([data]) }}
        mode='create'
      />
    })
  }

  const onViewGroup = (record: ToolGroup) => {
    setDrawerOptions({
      title: "View Tool Group",
      isOpen: true,
      component: <ToolGroupViewDrawer
        toolGroup={record}
        onDelete={(record: ToolGroup) => onDeleteGroup(record)}
        onEdit={(record: ToolGroup) => onEditGroup(record)}
        onDuplicate={(record: ToolGroup) => onDuplicateGroup(record)}
      />
    })
  }

  const onDeleteGroup = (record: ToolGroup) => {
    setDeleteModalOptions({ toolGroup: record, open: true, type: 'group' });
  }

  const onDuplicateGroup = (record: ToolGroup) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate group:', record);
  }

  // Delete handlers
  const onDeleteConfirm = () => {
    if (deleteModalOptions.type === 'tool') {
      setMutateToolObject(deleteModalOptions.agentTool ? [deleteModalOptions.agentTool] : []);
    } else {
      setMutateGroupObject(deleteModalOptions.toolGroup ? [deleteModalOptions.toolGroup] : []);
    }
    setDeleteModalOptions({ open: false, type: 'tool' });
  }

  const onDeleteCancel = () => {
    setDeleteModalOptions({ open: false, type: 'tool' });
  }

  const tabItems = [
    {
      key: 'tools',
      label: (
        <span>
          <FunctionOutlined />
          Agent Tools
        </span>
      ),
      children: (
        <>
          <Button size='large' type='primary' onClick={() => onCreateTool()}>
            <PlusOutlined />Create Tool
          </Button>
          <Divider />
          <AgentToolList
            onClick={(record: AgentTool) => onViewTool(record)}
            onDelete={(record: AgentTool) => onDeleteTool(record)}
            onEdit={(record: AgentTool) => onEditTool(record)}
            onDeploy={(record: AgentTool) => onDeployTool(record)}
            onValidate={(record: AgentTool) => onValidateTool(record)}
            onDuplicate={(record: AgentTool) => onDuplicateTool(record)}
            mutateObjects={mutateToolObject}
          />
        </>
      )
    },
    {
      key: 'groups',
      label: (
        <span>
          <GroupOutlined />
          Tool Groups
        </span>
      ),
      children: (
        <>
          <Button size='large' type='primary' onClick={() => onCreateGroup()}>
            <PlusOutlined />Create Group
          </Button>
          <Divider />
          <ToolGroupList
            onClick={(record: ToolGroup) => onViewGroup(record)}
            onDelete={(record: ToolGroup) => onDeleteGroup(record)}
            onEdit={(record: ToolGroup) => onEditGroup(record)}
            onDuplicate={(record: ToolGroup) => onDuplicateGroup(record)}
            mutateObjects={mutateGroupObject}
          />
        </>
      )
    }
  ];

  return (
    <>
      <ConfirmDeleteModal<AgentTool | ToolGroup>
        controller={deleteModalOptions.type === 'tool' ? new AgentToolController() : new ToolGroupController()}
        open={deleteModalOptions.open}
        objectToDelete={deleteModalOptions.type === 'tool' ? deleteModalOptions.agentTool : deleteModalOptions.toolGroup}
        onDelete={onDeleteConfirm}
        onCancel={onDeleteCancel}
      />
      <Typography.Title level={2}>
        Agent Tools
      </Typography.Title>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
      />
      <Drawer
        title={drawerOptions.title}
        placement="right"
        size="large"
        onClose={onCloseDrawer}
        open={drawerOptions.isOpen}
        extra={
          <Space>
            <Button onClick={onCloseDrawer}>Close</Button>
          </Space>
        }
      >
        {drawerOptions.component}
      </Drawer>
    </>
  );
};

export default AgentToolsComponent;

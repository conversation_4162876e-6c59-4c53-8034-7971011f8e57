import { IBasemo<PERSON>, IBasemodelController, IBaseModelSearchController, ListResponse } from "@/models/base-model";
import { Empty, Table, theme, Input, Space } from "antd";
import React, { useEffect } from "react";
import type { TableColumnsType } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

export interface DataTableParams<T extends IBasemodel, TC extends IBasemodelController<T, any>> {
    mutateItems?: T[];
    controller: TC;
    itemUpdateInterval : number;
    tableColumns : TableColumnsType<T>;
    noDataDetails? : (isError : boolean) => React.JSX.Element;
    invalidate? : boolean;
    onItemsValidatingChange? : (isValidating : boolean, validatingIdList : string[]) => void;
    onDataChange? : (entries : T[]) => void;
    shouldInvalidate : (entry : T) => boolean;
    rowKey?: string | ((record : T) => string);
    searchController?: IBaseModelSearchController<T>;
    searchPlaceholder?: string;
    enableSearch?: boolean;
}

interface ListRequest{
    count: number;
    nextToken?: string;
}

export default function DataTable<T extends IBasemodel, TC extends IBasemodelController<T, any>>(params : DataTableParams<T, TC>){
    const [kbQueryParams, setKbQueryParams] = React.useState<ListRequest>({ count: -1 });
    const [searchQuery, setSearchQuery] = React.useState<string>('');
    const [isSearchMode, setIsSearchMode] = React.useState<boolean>(false);

    // Use search or list based on search mode
    const listResult = params.controller.useList(kbQueryParams);
    const searchResult = params.searchController?.useSearch(searchQuery, 100);

    const { data, error, isLoading, mutate } = isSearchMode && searchResult ? searchResult : listResult;
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const { data: statusData, isValidating: statusIsLoading } = params.controller.useGet(pendingData, { refreshInterval: params.itemUpdateInterval, revalidateIfStale: true });

    useEffect(() => {
        if(params.onItemsValidatingChange) params.onItemsValidatingChange(statusIsLoading, pendingData)
    }, [statusIsLoading, pendingData])

    useEffect(() => {
        if(params.invalidate === undefined) return;
        mutate();
    }, [params.invalidate])

    useEffect(() => {
        if (params.mutateItems === undefined || params.mutateItems.length == 0) return;
        mutate(async (data: ListResponse<T> | undefined) => {
            if (data === undefined || params.mutateItems === undefined || params.mutateItems.length == 0) return data;

            const mutatedIds = params.mutateItems.map((item: T) => params.controller.getId(item))
            const filteredData = (data.entries ?? []).filter((item: T) => !mutatedIds.includes( params.controller.getId(item)))
            filteredData.push(...params.mutateItems);
            return { ...data, entries: filteredData };
        }, { revalidate: false })
    }, [params.mutateItems])


    useEffect(() => {
        const pendingDataUpdate = (data?.entries ?? []).filter((value: T) => params.shouldInvalidate(value)).map((value: T) =>  params.controller.getId(value)) ?? [];
        setPendingData(pendingData.filter((key: string) => pendingDataUpdate.includes(key)));

        const interval = setTimeout(() => {
            setPendingData(pendingDataUpdate);
        }, params.itemUpdateInterval);

        if(params.onDataChange) params.onDataChange(data?.entries ?? []);
        return () => clearTimeout(interval);
    }, [data])

    useEffect(() => {
        const pendingDataUpdate = statusData?.filter((value: T) => params.shouldInvalidate(value)).map((value: T) =>  params.controller.getId(value)) ?? [];
        const updatedKbs = statusData?.filter((value: T) => !params.shouldInvalidate(value)) ?? [];
        const updatedKbsIds = updatedKbs.map((item: T) =>  params.controller.getId(item));

        if (updatedKbs.length > 0 && (statusData ?? []).length > 0) {
            mutate(async (data: ListResponse<T> | undefined) => {
                if (data === undefined) return data;

                const dataMinusUpdates = (data.entries ?? []).filter((item: T) => !updatedKbsIds.includes( params.controller.getId(item)))
                dataMinusUpdates.push(...updatedKbs);

                return { ...data, entries: dataMinusUpdates };

            }, { revalidate: false })
        }

        if (pendingDataUpdate.length > 0 && (statusData ?? []).filter((arr1Item: T) => !pendingDataUpdate.includes( params.controller.getId(arr1Item))).length > 0) {
            const interval = setInterval(() => {
                if (pendingDataUpdate.length > 0) {
                    setPendingData(pendingDataUpdate);
                }
            }, params.itemUpdateInterval);
            return () => clearInterval(interval);
        }
    }, [statusData])

    const handleSearch = (value: string) => {
        setSearchQuery(value);
        setIsSearchMode(value.length > 0);
    };

    return (
        <>
            {params.enableSearch && params.searchController && (
                <Space style={{ marginBottom: 16 }}>
                    <Input.Search
                        placeholder={params.searchPlaceholder || "Search..."}
                        allowClear
                        onSearch={handleSearch}
                        onChange={(e) => handleSearch(e.target.value)}
                        style={{ width: 300 }}
                        prefix={<SearchOutlined />}
                    />
                </Space>
            )}
            <Table<T>
                rowSelection={{ type: 'checkbox' }}
                loading={isLoading}
                columns={params.tableColumns}
                dataSource={data?.entries}
                rowKey={params.rowKey ?? 'id'}
                pagination={{ position: ['bottomCenter'] }}
                locale={{ emptyText: <Empty description="No Data">{(params.noDataDetails) ? params.noDataDetails(error) : null}</Empty> }}
            />
        </>
    )
}
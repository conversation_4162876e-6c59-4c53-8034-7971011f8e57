import { Form, Input } from "antd";
import React from "react";
import { DummyTaskData } from "./dummy-task";

interface DummyTaskFormProps {
    value: DummyTaskData;
    onChange: (data: DummyTaskData) => void;
}

const DummyTaskForm: React.FC<DummyTaskFormProps> = ({ value, onChange }) => {
    return (
        <Form.Item label="Message">
            <Input 
                value={value.message} 
                onChange={(e) => onChange({ message: e.target.value })} 
            />
        </Form.Item>
    );
};

export default DummyTaskForm;
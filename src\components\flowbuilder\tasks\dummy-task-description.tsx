import React from "react";
import { Typography } from "antd";
import { DummyTaskProps } from "./dummy-task";

interface DummyTaskDescriptionProps {
    task: DummyTaskProps;
}

const DummyTaskDescription: React.FC<DummyTaskDescriptionProps> = ({ task }) => {
    return (
        <Typography.Text>
            Message: {task.taskData.message || "(empty)"}
        </Typography.Text>
    );
};

export default DummyTaskDescription;
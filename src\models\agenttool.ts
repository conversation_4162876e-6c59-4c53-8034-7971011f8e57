import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export enum AgentToolLanguage {
    JAVA = 'JAVA',
    CSHARP = 'CSHARP',
    PYTHON = 'PYTHON'
}

export enum AgentToolStatus {
    DRAFT = 'DRAFT',
    VALIDATING = 'VALIDATING',
    READY = 'READY',
    FAILED = 'FAILED',
    DEPLOYING = 'DEPLOYING',
    DEPLOYED = 'DEPLOYED'
}

export interface AgentToolInput {
    id: string;
    name: string;
    dataType: 'STRING' | 'INTEGER' | 'FLOAT' | 'BOOLEAN' | 'JSON';
    required: boolean;
    defaultValue?: string;
    description?: string;
}

export interface AgentTool extends IBasemodel {
    id: string;
    name: string;
    description: string;
    language: AgentToolLanguage;
    code: string;
    inputs: AgentToolInput[];
    status: AgentToolStatus;
    version?: number;
    deployedAt?: number;
    errorMessage?: string;
}

export enum AgentToolActions {
    EDIT,
    DELETE,
    DEPLOY,
    VALIDATE,
    DUPLICATE
}

export interface AgentToolCreateRequest {
    name: string;
    description: string;
    language: AgentToolLanguage;
    code: string;
    inputs: AgentToolInput[];
}

export interface AgentToolUpdateRequest {
    id: string;
    name: string;
    description: string;
    language: AgentToolLanguage;
    code: string;
    inputs: AgentToolInput[];
}

export interface AgentToolDeployRequest {
    id: string;
}

export interface AgentToolValidateRequest {
    id: string;
}

export class AgentToolController implements IBasemodelController<AgentTool, AgentToolActions> {
    getId: (item: AgentTool) => string = (item) => item.id;

    useGet = (ids: string[], options?: SWRConfiguration) =>
        Fetcher2.SWRMultiTemplate<AgentTool>(
            ids.map((id) => APIRoutes.AgentToolController.GET.replace("{id}", id)),
            { method: 'GET' },
            options
        )

    can = (action: AgentToolActions, onItem: AgentTool) => {
        switch (action) {
            case AgentToolActions.EDIT:
                return onItem.status !== AgentToolStatus.DEPLOYING;
            case AgentToolActions.DELETE:
                return onItem.status !== AgentToolStatus.DEPLOYING;
            case AgentToolActions.DEPLOY:
                return onItem.status === AgentToolStatus.READY;
            case AgentToolActions.VALIDATE:
                return onItem.status === AgentToolStatus.DRAFT || onItem.status === AgentToolStatus.FAILED;
            case AgentToolActions.DUPLICATE:
                return true;
            default:
                return false;
        }
    }

    useList = (request: ListRequest) =>
        Fetcher2.SWRTemplate<ListResponse<AgentTool>>(
            APIRoutes.AgentToolController.LIST,
            { method: 'GET', queryString: request }
        )

    useDelete = (id: string) =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.DELETE,
            { method: 'DELETE', urlPlaceholders: { id: id } }
        )

    useCreate = () =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.CREATE,
            { method: 'POST' }
        )

    useUpdate = () =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.EDIT,
            { method: 'PUT' }
        )

    useDeploy = (id: string) =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.DEPLOY,
            { method: 'POST', urlPlaceholders: { id: id } }
        )

    useValidate = (id: string) =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.VALIDATE,
            { method: 'POST', urlPlaceholders: { id: id } }
        )

    useDuplicate = (id: string) =>
        Fetcher2.SWRMutationTemplate<AgentTool>(
            APIRoutes.AgentToolController.DUPLICATE,
            { method: 'POST', urlPlaceholders: { id: id } }
        )
}

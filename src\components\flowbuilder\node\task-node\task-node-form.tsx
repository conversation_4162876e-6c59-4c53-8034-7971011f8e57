import { <PERSON><PERSON>, <PERSON>, <PERSON>, Space } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { DummyTaskData, TaskNodeModelData } from "./task-node-model";
import React from "react";
import SubmitButton from "@/components/submit-button";
import TaskTypeSelect, { defaultTaskTypes } from "@/components/flowbuilder/tasks/task-type-select";
import DummyTaskForm from "@/components/flowbuilder/tasks/dummy-task-form";

// Task form registry - maps task types to their form components
const taskForms: Record<string, React.ComponentType<any>> = {
    'dummy': DummyTaskForm
};

export default function TaskNodeForm(props: BaseNodeFormElement<TaskNodeModelData>) {
    const [form] = Form.useForm<TaskNodeModelData>();
    const [selectedTaskType, setSelectedTaskType] = React.useState<string | null>(null);

    const handleAddTask = () => {
        if (!selectedTaskType) return;
        
        const newTasks = [...(props.data.tasks || [])];
        
        if (selectedTaskType === 'dummy') {
            newTasks.push({
                name: 'Dummy Task',
                taskData: { message: '' } as DummyTaskData
            });
        }
        
        props.onChange({ ...props.data, tasks: newTasks });
        setSelectedTaskType(null);
    };

    const handleTaskChange = (index: number, taskData: any) => {
        const newTasks = [...props.data.tasks];
        newTasks[index] = { ...newTasks[index], taskData };
        props.onChange({ ...props.data, tasks: newTasks });
    };

    const handleDeleteTask = (index: number) => {
        const newTasks = [...props.data.tasks];
        newTasks.splice(index, 1);
        props.onChange({ ...props.data, tasks: newTasks });
    };

    // Render the appropriate form based on task type
    const renderTaskForm = (task: any, index: number) => {
        const taskType = task.name.toLowerCase().replace(' ', '-');
        const TaskFormComponent = taskForms[taskType];
        
        if (!TaskFormComponent) {
            return <div>Unknown task type: {task.name}</div>;
        }
        
        return (
            <TaskFormComponent 
                value={task.taskData} 
                onChange={(data: any) => handleTaskChange(index, data)} 
            />
        );
    };

    return (
        <Form
            form={form}
            layout="vertical"
            initialValues={props.data}
            onFinish={() => {
                props.onChange(form.getFieldsValue());
                props.onCancel();
            }}
        >
            <Space direction="vertical" style={{ width: '100%' }}>
                {props.data.tasks?.map((task, index) => (
                    <Card 
                        key={index} 
                        title={`Task ${index + 1}: ${task.name}`}
                        extra={
                            <Button 
                                danger 
                                onClick={() => handleDeleteTask(index)}
                            >
                                Remove
                            </Button>
                        }
                    >
                        {renderTaskForm(task, index)}
                    </Card>
                ))}
                
                <TaskTypeSelect
                    value={selectedTaskType}
                    onChange={setSelectedTaskType}
                    onAddTask={handleAddTask}
                    options={defaultTaskTypes}
                />

                <Space>
                    <SubmitButton form={form}>Save Changes</SubmitButton>
                    <Button onClick={props.onCancel}>Cancel</Button>
                </Space>
            </Space>
        </Form>
    );
}

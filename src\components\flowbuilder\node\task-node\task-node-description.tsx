import { Tag, Space } from "antd";
import { TaskNodeModelData } from "./task-node-model";
import { BaseNodeData } from "../base-node";

function TaskNodeDescription(props: BaseNodeData<TaskNodeModelData>) {
    return (
        <Space direction="vertical">
            {props.nodeData.tasks.map((task, index) => (
                <div key={index}>
                    Task {index + 1}: <Tag color="blue-inverse" bordered={false}>{task.name}</Tag>
                </div>
            ))}
            {props.nodeData.tasks.length === 0 && <div>No tasks configured</div>}
        </Space>
    );
}

export default TaskNodeDescription;